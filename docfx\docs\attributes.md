# Attributes Reference

HotPreview provides several attributes to control preview generation and component organization. This reference covers all available attributes and their parameters.

## Preview Attributes

### `[Preview]`

The main attribute for defining component previews.

**Applies to:** Classes, Methods  
**Namespace:** `HotPreview`

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `displayName` | `string?` | Optional title for the preview, determining how it appears in navigation UI. "/" delimiters can be used to indicate hierarchy. |
| `uiComponent` | `Type?` | Optional type of the UI component this preview represents. |

#### Constructors

```csharp
[Preview()]
[Preview(string? displayName)]
[Preview(Type uiComponent)]
[Preview(string? displayName, Type? uiComponent)]
```

#### Examples

```csharp
#if PREVIEWS
// Basic preview
[Preview]
public static MyComponent BasicPreview() => new MyComponent();

// Named preview
[Preview("Loading State")]
public static MyComponent LoadingPreview() => new MyComponent { IsLoading = true };

// Preview with explicit component type
[Preview(typeof(MyComponent))]
public static MyComponent Preview() => new MyComponent();

// Hierarchical preview names
[Preview("States/Loading")]
public static MyComponent LoadingState() => new MyComponent { IsLoading = true };

[Preview("States/Error")]
public static MyComponent ErrorState() => new MyComponent { HasError = true };
#endif
```

### `[PreviewCommand]`

Defines commands that can be executed from the DevTools UI.

**Applies to:** Methods  
**Namespace:** `HotPreview`

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `displayName` | `string?` | Optional display name for the command, determining how it appears in navigation UI. "/" delimiters can be used to indicate hierarchy. |

#### Constructors

```csharp
[PreviewCommand()]
[PreviewCommand(string? displayName)]
```

#### Examples

```csharp
#if PREVIEWS
[PreviewCommand]
public static void ResetData() => DataService.Reset();

[PreviewCommand("Clear Cache")]
public static void ClearCache() => CacheService.Clear();

[PreviewCommand("Database/Seed Test Data")]
public static void SeedTestData() => DatabaseService.SeedTestData();
#endif
```

## Component Attributes

### `[UIComponent]`

Explicitly marks a class as a UI component and optionally provides a display name.

**Applies to:** Classes  
**Namespace:** `HotPreview`

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `displayName` | `string?` | Optional display name for the UI component, if it's different from the type name. |

#### Constructors

```csharp
[UIComponent()]
[UIComponent(string? displayName)]
```

#### Examples

```csharp
[UIComponent]
public partial class MyComponent : ContentView
{
    // Component implementation
}

[UIComponent("Custom Button")]
public partial class MyCustomButton : Button
{
    // Component implementation
}
```

### `[AutoGeneratePreview]`

Controls whether auto-generated previews should be created for a UI component.

**Applies to:** Classes  
**Namespace:** `HotPreview`

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `autoGenerate` | `bool` | Controls whether auto-generated previews should be created for this component. When set to false, auto-generation is disabled. |

#### Constructor

```csharp
[AutoGeneratePreview(bool autoGenerate)]
```

#### Examples

```csharp
// Disable auto-generation for this component
[AutoGeneratePreview(false)]
public partial class ComplexComponent : ContentView
{
    // This component won't have auto-generated previews
    // Only manually defined [Preview] methods will be used
}

// Explicitly enable auto-generation (default behavior)
[AutoGeneratePreview(true)]
public partial class SimpleComponent : ContentView
{
    // This component will have auto-generated previews
}
```

## Assembly-Level Attributes

### `[UIComponentCategory]`

Groups UI components into categories at the assembly level.

**Applies to:** Assembly  
**Namespace:** `HotPreview`  
**Allow Multiple:** Yes

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `name` | `string` | The name of the category. |
| `uiComponents` | `Type[]` | Array of UI component types to include in this category. |

#### Constructor

```csharp
[UIComponentCategory(string name, params Type[] uiComponents)]
```

#### Example

```csharp
// In AssemblyInfo.cs or any source file
using HotPreview;

[assembly: UIComponentCategory("Forms", typeof(LoginForm), typeof(RegisterForm), typeof(ContactForm))]
[assembly: UIComponentCategory("Navigation", typeof(TabBar), typeof(NavigationBar), typeof(Breadcrumb))]
[assembly: UIComponentCategory("Data Display", typeof(DataGrid), typeof(Chart), typeof(Table))]
```

## Usage Notes

### Conditional Compilation

It's recommended to wrap preview-related code in conditional compilation blocks:

```csharp
#if PREVIEWS
[Preview]
public static MyComponent Preview() => new MyComponent();

[PreviewCommand]
public static void ResetToDefaults() => /* implementation */;
#endif
```

### Hierarchical Organization

Use "/" delimiters in display names to create hierarchical organization:

```csharp
#if PREVIEWS
[Preview("States/Loading")]
public static MyComponent LoadingState() => /* implementation */;

[Preview("States/Error")]
public static MyComponent ErrorState() => /* implementation */;

[Preview("Variants/Large")]
public static MyComponent LargeVariant() => /* implementation */;

[Preview("Variants/Small")]
public static MyComponent SmallVariant() => /* implementation */;
#endif
```

### Best Practices

1. **Use descriptive names** for previews with multiple variants
2. **Group related previews** using hierarchical naming
3. **Provide sample data** that represents realistic usage scenarios
4. **Consider edge cases** like empty states, loading states, and error states
5. **Use conditional compilation** to exclude preview code from release builds