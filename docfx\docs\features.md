# Features

HotPreview provides a comprehensive set of features for UI component development and testing in .NET applications.

## Core Features

### Automatic Component Discovery

HotPreview automatically discovers UI components in your application based on their inheritance hierarchy:

- **Pages**: Components that derive from platform-specific page types (e.g., `Microsoft.Maui.Controls.Page`)
- **Controls**: Components that derive from view types (e.g., `Microsoft.Maui.Controls.View`)

Components are discovered if they have:
- A parameterless constructor, or
- Constructor parameters that can be resolved via dependency injection

### Custom Preview Definitions

Define custom previews for any component using the `[Preview]` attribute:

```csharp
#if PREVIEWS
[Preview]
public static MyComponent DefaultState() => new MyComponent();

[Preview("Loading")]
public static MyComponent LoadingState() => new MyComponent { IsLoading = true };

[Preview("With Error")]
public static MyComponent ErrorState() => new MyComponent { Error = "Something went wrong" };
#endif
```

### Live Preview Navigation

- Click any component or preview in the DevTools to navigate directly to it in your running app
- Real-time synchronization between DevTools and your application
- Instant feedback during development

### Developer Tools Integration

The HotPreview DevTools provide:

- **Component Tree View**: Browse all discovered components and their previews
- **Preview Gallery**: Visual gallery of component states
- **Command Execution**: Execute preview commands from the UI
- **Auto-launch**: Automatically starts when you build your app in Debug mode

## Advanced Features

### Preview Commands

Define executable commands that can be triggered from the DevTools:

```csharp
#if PREVIEWS
[PreviewCommand]
public static void ResetUserData() => UserService.Reset();

[PreviewCommand("Simulate Network Error")]
public static void SimulateNetworkError() => NetworkService.SimulateError();
#endif
```

### Component Categories

Organize components into logical categories using assembly-level attributes:

```csharp
[assembly: UIComponentCategory("Forms", typeof(LoginForm), typeof(RegisterForm))]
[assembly: UIComponentCategory("Navigation", typeof(TabBar), typeof(NavMenu))]
```

### Hierarchical Preview Organization

Use "/" delimiters in preview names to create hierarchical organization:

```csharp
#if PREVIEWS
[Preview("States/Loading")]
public static MyComponent LoadingState() => /* ... */;

[Preview("States/Error")]
public static MyComponent ErrorState() => /* ... */;

[Preview("Variants/Large")]
public static MyComponent LargeVariant() => /* ... */;
#endif
```

### Auto-generation Control

Control automatic preview generation on a per-component basis:

```csharp
// Disable auto-generation for complex components
[AutoGeneratePreview(false)]
public partial class ComplexComponent : ContentView
{
    // Only manually defined previews will be shown
}
```

## Platform Support

### .NET MAUI

Full support for .NET MAUI applications with:
- Automatic discovery of Pages and Views
- Integration with MAUI's dependency injection container
- Platform-specific icon support
- Cross-platform compatibility (Windows, macOS, iOS, Android)

### Extensible Architecture

The framework is designed to support additional platforms:
- Platform-agnostic core components
- Pluggable platform-specific implementations
- Consistent API across platforms

## Development Workflow Integration

### Build Integration

- MSBuild tasks automatically launch DevTools during Debug builds
- Seamless integration with existing development workflows
- No additional setup required after initial configuration

### Conditional Compilation

Built-in support for conditional compilation to exclude preview code from release builds:

```csharp
#if PREVIEWS
// Preview code here is automatically excluded from Release builds
#endif
```

### Hot Reload Support

Works seamlessly with hot reload technologies:
- Preview updates reflect immediately during development
- No need to restart the application or DevTools
- Maintains state during code changes

## Visual Testing Support

### Snapshot Testing

Integration with visual regression testing utilities:
- Capture component snapshots
- Compare visual differences
- Automated visual regression detection

### Test Data Management

Built-in patterns for managing test data:
- Preview data classes for consistent test scenarios
- Sample data generation utilities
- Edge case simulation

## Performance Features

### Efficient Discovery

- Optimized reflection-based component discovery
- Roslyn-based static analysis for build-time optimization
- Minimal runtime overhead

### Resource Management

- Efficient memory usage during preview navigation
- Proper cleanup of preview instances
- Optimized communication between DevTools and application

## Cross-Platform Communication

### JSON-RPC Protocol

- Platform-agnostic communication protocol
- Real-time bidirectional communication
- Type-safe method invocation
- Extensible for custom tooling scenarios
