---
_layout: landing
---

# HotPreview

**A cross-platform UI component preview system for .NET**

HotPreview lets you easily work on pages/controls in your app in isolation, without the need to run the app, navigate to the page, and supply any test data.

## What is HotPreview?

Previews are similar to stories in [Storybook](https://storybook.js.org/) for JavaScript and Previews in [SwiftUI/Xcode](https://developer.apple.com/documentation/xcode/previewing-your-apps-interface-in-xcode) and [Jetpack Compose/Android Studio](https://developer.android.com/develop/ui/compose/tooling/previews) - but for .NET UI.

The framework itself is cross platform, intended to work with (most) any .NET UI platform - it has a platform agnostic piece and platform specific piece, with the platform piece pluggable. Initial support is for .NET MAUI.

## Key Features

- **Automatic component discovery** - Automatically finds and creates previews for your UI components
- **Custom preview definitions** - Define multiple previews with different data and states
- **Live preview** - Navigate directly to component previews in your running app
- **Cross-platform** - Works with .NET MAUI with extensible platform support
- **Developer-friendly** - Integrates seamlessly with your existing development workflow

## Quick Start

1. Install the preview DevTools: `dotnet tool install -g HotPreview.DevTools`
2. Add the appropriate platform NuGet package to your app (e.g., `HotPreview.App.Maui`)
3. Build and run your app in Debug mode

That's it! HotPreview DevTools will launch automatically and connect to your app, showing all discoverable UI components and their previews.

## Learn More

- [Getting Started Guide](docs/getting-started.md) - Comprehensive setup and usage instructions
- [Attributes Reference](docs/attributes.md) - Complete documentation of all HotPreview attributes
- [API Documentation](api/index.md) - Generated API reference

## Example

```csharp
#if PREVIEWS
[Preview("Default State")]
public static MyComponent DefaultPreview() => new MyComponent();

[Preview("Loading State")]
public static MyComponent LoadingPreview() => new MyComponent { IsLoading = true };

[Preview("With Data")]
public static MyComponent WithDataPreview() => new MyComponent 
{ 
    Items = PreviewData.GetSampleItems() 
};
#endif
```
