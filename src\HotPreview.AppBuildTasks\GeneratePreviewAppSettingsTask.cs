using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
using Microsoft.Build.Framework;

namespace HotPreview.AppBuildTasks
{
    public class GeneratePreviewAppSettingsTask : Microsoft.Build.Utilities.Task
    {
        [Required]
        public required string ProjectPath { get; set; }

        [Required]
        public required string OutputPath { get; set; }

        [Required]
        public required string PlatformPreviewApplication { get; set; }

        private static string HotPreviewConfigDir
        {
            get
            {
                string localAppDataDir = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                return Path.Combine(localAppDataDir, "HotPreview");
            }
        }

        private static string DevToolsLaunchingLockFilePath => Path.Combine(HotPreviewConfigDir, "devtools-launching.lock");

        private static string DevToolsConnectionJsonPath => Path.Combine(HotPreviewConfigDir, "devToolsConnectionSettings.json");

        public override bool Execute()
        {
            try
            {
                string projectPath = Path.GetFullPath(ProjectPath);
                string outputPath = Path.GetFullPath(OutputPath);
                string? outputDirectory = Path.GetDirectoryName(outputPath);

                if (outputDirectory is not null && !Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                }

                // Get the connection string from devToolsConnectionSettings.json. That file exists while
                // DevToolsApp is running, which we'll launch if needed.

                string connectionString = "";
                string jsonPath = DevToolsConnectionJsonPath;

                // Check if the devtools app is running, launching it if not.
                if (!File.Exists(jsonPath))
                {
                    if (!LaunchDevToolsAppWithLock())
                    {
                        return true;   // Errors already logged, but don't fail the build
                    }
                }

                if (!File.Exists(jsonPath))
                {
                    Log.LogWarning($"Hot Preview: devtools is running, but the {jsonPath} file doesn't exist.");
                    return false;
                }

                try
                {
                    string jsonContent = File.ReadAllText(jsonPath);
                    using (JsonDocument doc = JsonDocument.Parse(jsonContent))
                    {
                        if (doc.RootElement.TryGetProperty("app", out JsonElement appElement) &&
                            appElement.ValueKind == JsonValueKind.String)
                        {
                            connectionString = appElement.GetString() ?? "";
                        }
                    }
                }
                catch
                {
                    // If any error occurs, use empty string as fallback
                    connectionString = "";
                }

                string content = $$"""
// <auto-generated/>
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

namespace HotPreview.SharedModel
{
    public static class PreviewApplicationInitializer
    {
        [ModuleInitializer]
        public static void Initialize()
        {
            var previewApp = {{PlatformPreviewApplication}};
            if (previewApp != null)
            {
                previewApp.MainAssembly = typeof(PreviewApplicationInitializer).Assembly;
                previewApp.ProjectPath = @"{{projectPath.Replace("\"", "\"\"")}}";
                previewApp.ToolingConnectionString = @"{{connectionString.Replace("\"", "\"\"")}}";
                {{(connectionString != "" ? "previewApp.StartToolingConnection();" : "// No tooling connection, so not starting connection")}}
            }
        }
    }
}
""";

                File.WriteAllText(outputPath, content);
                return true;
            }
            catch (Exception ex)
            {
                Log.LogWarning($"Hot Preview: Error generating preview app settings: {ex.Message}");
                return false;
            }
        }

        // TODO: This isn't currently used. Consider making use of it again or remove it.
        private bool IsDevToolsAppRunning()
        {
            try
            {
                Process[] processes = Process.GetProcessesByName("HotPreview.DevToolsApp");
                if (processes.Length == 0)
                {
                    return false;
                }

                // Check if the app is currently launching - if so, treat it as not running
                if (File.Exists(DevToolsLaunchingLockFilePath))
                {
                    return false; // App is launching, treat as not running
                }

                Log.LogMessage(MessageImportance.High, "Hot Preview: devtools is already running");
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Launches the DevTools app with file-based locking to handle concurrent execution.
        /// Different processes (like MSBuild and devenv when doing a rebuild all in VS) may execute
        /// this task during the build. This method uses an exclusive file lock to ensure the app
        /// is only launched once, with any other launch attempts waiting on the first one to complete.
        /// </summary>
        private bool LaunchDevToolsAppWithLock()
        {
            string lockFilePath = DevToolsLaunchingLockFilePath;
            string lockFileDirectory = Path.GetDirectoryName(lockFilePath);

            if (!Directory.Exists(lockFileDirectory))
            {
                Directory.CreateDirectory(lockFileDirectory);
            }

            // Try to create and hold an exclusive lock on the file
            try
            {
                string lockContent = $"Launched by process {Process.GetCurrentProcess().ProcessName} {Process.GetCurrentProcess().Id} at {DateTime.UtcNow:O}";
                using var lockFile = LockFile.Create(lockFilePath, lockContent);

                Log.LogMessage(MessageImportance.Low, "Hot Preview: Acquired launch lock, launching devtools app...");

                // Launch the app while holding the exclusive lock
                return LaunchDevToolsApp();

                // Lock file will be automatically deleted when disposed
            }
            catch (IOException ex) when (ex.HResult == unchecked((int)0x80070020)) // File is being used by another process
            {
                Log.LogMessage(MessageImportance.Low, "Hot Preview: Another process is launching devtools app, waiting...");
                return WaitForLaunchCompletion(lockFilePath);
            }
            catch (Exception ex)
            {
                Log.LogWarning($"Hot Preview: Failed to create launch lock file: {ex.Message}");
                return false;
            }
        }

        private bool WaitForLaunchCompletion(string lockFilePath)
        {
            var timeout = TimeSpan.FromSeconds(10); // Longer timeout for waiting on another process
            var stopwatch = Stopwatch.StartNew();

            while (stopwatch.Elapsed < timeout)
            {
                // Check if the lock file is gone (launch completed)
                if (!File.Exists(lockFilePath))
                {
                    // Lock file is gone, check if the settings file exists
                    if (File.Exists(DevToolsConnectionJsonPath))
                    {
                        Log.LogMessage(MessageImportance.Low, "Hot Preview: devtools launched by a different build task");
                        return true;
                    }
                    else
                    {
                        Log.LogWarning("Hot Preview: A different build task finished launching devtools but the settings file doesn't exist");
                        return false;
                    }
                }

                Thread.Sleep(200); // Check every 200ms
            }

            // Timeout reached - the other process might have failed
            Log.LogWarning("Hot Preview: Timeout waiting for another build task to launch devtools");
            return false;
        }

        private bool LaunchDevToolsApp()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "hotpreview",
                    Arguments = "--launch",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                Log.LogMessage(MessageImportance.High, "Hot Preview: Launching DevTools...");

                using var process = Process.Start(startInfo);
                if (process is null)
                {
                    Log.LogWarning("Hot Preview: Failed to start hotpreview process");
                    return false;
                }

                process.WaitForExit();

                if (process.ExitCode != 0)
                {
                    string errorOutput = process.StandardError.ReadToEnd();
                    Log.LogWarning($"Hot Preview: hotpreview failed with exit code {process.ExitCode}: {errorOutput}");
                    return false;
                }

                // Wait for the connection settings file to exist or 5 seconds timeout
                if (!WaitForConnectionSettingsFile())
                {
                    return false;
                }

                Log.LogMessage(MessageImportance.High, $"Hot Preview: Launched DevTools");
                return true;
            }
            // When the hotpreview executable is not found, an E_FAIL Win32Exception is thrown with the messaage below.
            // For English systems, match on the message.
            catch (Win32Exception ex) when (ex.Message.Contains("The system cannot find the file specified"))
            {
                Log.LogWarning("Hot Preview: hotpreview not found.");
                Log.LogWarning("Hot Preview: Install it via e.g.: dotnet tool install --global HotPreview.DevTools");
                return false;
            }
            // In other cases, including non-English systems, log a more generic message that covers the not installed case too.
            catch (Exception ex)
            {
                Log.LogWarning($"Hot Preview: Error launching hotpreview: {ex}");
                Log.LogWarning("Hot Preview: Ensure it is installed via e.g.: dotnet tool install --global HotPreview.DevTools");
                return false;
            }
        }

        private bool WaitForConnectionSettingsFile()
        {
            string jsonPath = DevToolsConnectionJsonPath;

            var timeout = TimeSpan.FromSeconds(5);
            var stopwatch = Stopwatch.StartNew();

            while (stopwatch.Elapsed < timeout)
            {
                if (File.Exists(jsonPath))
                {
                    return true;
                }
                Thread.Sleep(100); // Check every 100ms
            }

            // Timeout reached - log error message
            Log.LogWarning($"Hot Preview: hotpreview launched but the {jsonPath} file didn't get created");
            return false;
        }
    }
}
